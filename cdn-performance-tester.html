<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN性能测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .config-section {
            margin-bottom: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }

        .config-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        textarea, input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        textarea:focus, input:focus {
            outline: none;
            border-color: #4facfe;
        }

        textarea {
            resize: vertical;
            min-height: 100px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 120px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            width: 0%;
            transition: width 0.3s;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #555;
        }

        .results-section {
            margin-top: 30px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background: white;
        }

        .results-table {
            width: 100%;
            min-width: 800px;
            border-collapse: collapse;
            background: white;
        }

        .results-table th,
        .results-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .results-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            cursor: pointer;
            user-select: none;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .results-table th:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .results-table tr:hover {
            background: #f8f9fa;
        }

        .results-table td:first-child {
            max-width: 200px;
        }

        .results-table td:nth-child(2) {
            max-width: 250px;
        }

        .results-table td:nth-child(3) {
            max-width: 100px;
            text-align: center;
        }

        .results-table td:nth-child(4) {
            max-width: 80px;
            text-align: center;
        }

        .status-success {
            color: #28a745;
            font-weight: 600;
        }

        .status-error {
            color: #dc3545;
            font-weight: 600;
        }

        .fastest {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            font-weight: 600;
        }

        /* CDN背景颜色 */
        .cdn-color-0 { background-color: rgba(79, 172, 254, 0.1); }
        .cdn-color-1 { background-color: rgba(255, 193, 7, 0.1); }
        .cdn-color-2 { background-color: rgba(40, 167, 69, 0.1); }
        .cdn-color-3 { background-color: rgba(220, 53, 69, 0.1); }
        .cdn-color-4 { background-color: rgba(102, 126, 234, 0.1); }
        .cdn-color-5 { background-color: rgba(255, 99, 132, 0.1); }
        .cdn-color-6 { background-color: rgba(54, 162, 235, 0.1); }
        .cdn-color-7 { background-color: rgba(255, 159, 64, 0.1); }
        .cdn-color-8 { background-color: rgba(153, 102, 255, 0.1); }
        .cdn-color-9 { background-color: rgba(75, 192, 192, 0.1); }

        /* 最快结果的特殊处理 */
        .fastest.cdn-color-0 { background: linear-gradient(135deg, rgba(79, 172, 254, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-1 { background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-2 { background: linear-gradient(135deg, rgba(40, 167, 69, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-3 { background: linear-gradient(135deg, rgba(220, 53, 69, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-4 { background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-5 { background: linear-gradient(135deg, rgba(255, 99, 132, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-6 { background: linear-gradient(135deg, rgba(54, 162, 235, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-7 { background: linear-gradient(135deg, rgba(255, 159, 64, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-8 { background: linear-gradient(135deg, rgba(153, 102, 255, 0.3), rgba(212, 237, 218, 0.8)); }
        .fastest.cdn-color-9 { background: linear-gradient(135deg, rgba(75, 192, 192, 0.3), rgba(212, 237, 218, 0.8)); }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-top: 4px solid #4facfe;
        }

        .summary-card h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .summary-card .value {
            font-size: 1.5em;
            font-weight: 600;
            color: #4facfe;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
            }

            button {
                width: 100%;
            }

            .results-table {
                font-size: 12px;
                min-width: 600px;
            }

            .results-table th,
            .results-table td {
                padding: 8px 4px;
            }

            .results-table td:first-child {
                max-width: 120px;
            }

            .results-table td:nth-child(2) {
                max-width: 150px;
            }

            .summary-cards {
                grid-template-columns: 1fr;
            }

            .config-section {
                padding: 15px;
            }

            .table-container {
                margin: 0 -20px;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CDN性能测试工具</h1>
            <p>测试多个CDN的文件加载性能，找出最优解决方案</p>
        </div>
        
        <div class="content">
            <!-- 配置区域 -->
            <div class="config-section">
                <h3>📋 测试配置</h3>

                <div class="form-group">
                    <label>配置模式：</label>
                    <div style="margin-bottom: 15px;">
                        <label style="display: inline-flex; align-items: center; margin-right: 20px; font-weight: normal;">
                            <input type="radio" name="configMode" value="unified" checked onchange="toggleConfigMode()" style="margin-right: 8px;">
                            统一配置（所有CDN使用相同文件列表）
                        </label>
                        <label style="display: inline-flex; align-items: center; font-weight: normal;">
                            <input type="radio" name="configMode" value="individual" onchange="toggleConfigMode()" style="margin-right: 8px;">
                            独立配置（每个CDN使用不同文件列表）
                        </label>
                    </div>
                </div>

                <!-- 统一配置模式 -->
                <div id="unifiedConfig">
                    <div class="form-group">
                        <label for="cdnUrls">CDN地址列表（每行一个）：</label>
                        <textarea id="cdnUrls" placeholder="https://cdn1.example.com&#10;https://cdn2.example.com&#10;https://cdnjs.cloudflare.com">https://cdnjs.cloudflare.com/ajax/libs
https://cdn.jsdelivr.net/npm
https://unpkg.com</textarea>
                    </div>

                    <div class="form-group">
                        <label for="testFiles">测试文件列表（每行一个）：</label>
                        <textarea id="testFiles" placeholder="jquery@3.6.0/dist/jquery.min.js&#10;bootstrap@5.1.3/dist/css/bootstrap.min.css">jquery@3.6.0/dist/jquery.min.js
bootstrap@5.1.3/dist/css/bootstrap.min.css
vue@3.2.31/dist/vue.global.min.js
lodash@4.17.21/lodash.min.js</textarea>
                    </div>
                </div>

                <!-- 独立配置模式 -->
                <div id="individualConfig" style="display: none;">
                    <div class="form-group">
                        <label>CDN配置（格式：CDN地址|文件1,文件2,文件3）：</label>
                        <div style="margin-bottom: 10px; padding: 10px; background: #e3f2fd; border-radius: 5px; font-size: 12px; color: #1565c0;">
                            <strong>示例格式：</strong><br>
                            https://cdnjs.cloudflare.com/ajax/libs|jquery/3.6.0/jquery.min.js,bootstrap/5.1.3/css/bootstrap.min.css<br>
                            https://cdn.jsdelivr.net/npm|vue@3/dist/vue.global.min.js,lodash@4/lodash.min.js
                        </div>
                        <textarea id="cdnConfigs" rows="8" placeholder="https://cdnjs.cloudflare.com/ajax/libs|jquery/3.6.0/jquery.min.js,bootstrap/5.1.3/css/bootstrap.min.css&#10;https://cdn.jsdelivr.net/npm|vue@3/dist/vue.global.min.js,lodash@4/lodash.min.js">https://cdnjs.cloudflare.com/ajax/libs|jquery/3.6.0/jquery.min.js,bootstrap/5.1.3/css/bootstrap.min.css,vue/3.2.31/dist/vue.global.min.js
https://cdn.jsdelivr.net/npm|jquery@3.6.0/dist/jquery.min.js,bootstrap@5.1.3/dist/css/bootstrap.min.css,lodash@4.17.21/lodash.min.js
https://unpkg.com|jquery@3.6.0/dist/jquery.min.js,vue@3.2.31/dist/vue.global.min.js,lodash@4.17.21/lodash.min.js</textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label for="timeout">超时时间（秒）：</label>
                    <input type="number" id="timeout" value="10" min="1" max="60">
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="button-group">
                <button class="btn-primary" onclick="startTest()">🚀 开始测试</button>
                <button class="btn-secondary" onclick="saveConfig()">💾 保存配置</button>
                <button class="btn-warning" onclick="loadConfig()">📂 加载配置</button>
                <button class="btn-success" onclick="exportResults()">📊 导出结果</button>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">准备开始测试...</div>
            </div>
            
            <!-- 结果区域 -->
            <div class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h3>📈 测试结果</h3>
                    <button class="btn-secondary" onclick="clearResults()">🗑️ 清空结果</button>
                </div>
                
                <!-- 汇总卡片 -->
                <div class="summary-cards" id="summaryCards"></div>
                
                <!-- 结果表格 -->
                <div class="table-container">
                    <table class="results-table" id="resultsTable">
                        <thead>
                            <tr>
                                <th onclick="sortTable(0)" title="点击排序">CDN地址 ↕️</th>
                                <th onclick="sortTable(1)" title="点击排序">文件名 ↕️</th>
                                <th onclick="sortTable(2)" title="点击排序">加载时间(ms) ↕️</th>
                                <th onclick="sortTable(3)" title="点击排序">状态 ↕️</th>
                            </tr>
                        </thead>
                        <tbody id="resultsBody"></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        let testResults = [];
        let sortDirection = {};

        // 切换配置模式
        function toggleConfigMode() {
            const mode = document.querySelector('input[name="configMode"]:checked').value;
            const unifiedConfig = document.getElementById('unifiedConfig');
            const individualConfig = document.getElementById('individualConfig');

            if (mode === 'unified') {
                unifiedConfig.style.display = 'block';
                individualConfig.style.display = 'none';
            } else {
                unifiedConfig.style.display = 'none';
                individualConfig.style.display = 'block';
            }
        }

        // 解析配置
        function parseConfig() {
            const mode = document.querySelector('input[name="configMode"]:checked').value;
            const timeout = parseInt(document.getElementById('timeout').value) * 1000;

            if (mode === 'unified') {
                // 统一配置模式
                const cdnUrls = document.getElementById('cdnUrls').value.trim().split('\n').filter(url => url.trim());
                const testFiles = document.getElementById('testFiles').value.trim().split('\n').filter(file => file.trim());

                if (cdnUrls.length === 0 || testFiles.length === 0) {
                    throw new Error('请输入CDN地址和测试文件！');
                }

                const testPairs = [];
                for (const cdn of cdnUrls) {
                    for (const file of testFiles) {
                        testPairs.push({ cdn: cdn.trim(), file: file.trim() });
                    }
                }
                return { testPairs, timeout };

            } else {
                // 独立配置模式
                const cdnConfigs = document.getElementById('cdnConfigs').value.trim().split('\n').filter(line => line.trim());

                if (cdnConfigs.length === 0) {
                    throw new Error('请输入CDN配置！');
                }

                const testPairs = [];
                for (const config of cdnConfigs) {
                    const parts = config.trim().split('|');
                    if (parts.length !== 2) {
                        throw new Error(`配置格式错误: ${config}\n正确格式: CDN地址|文件1,文件2,文件3`);
                    }

                    const cdn = parts[0].trim();
                    const files = parts[1].split(',').map(f => f.trim()).filter(f => f);

                    if (files.length === 0) {
                        throw new Error(`CDN ${cdn} 没有配置测试文件！`);
                    }

                    for (const file of files) {
                        testPairs.push({ cdn, file });
                    }
                }
                return { testPairs, timeout };
            }
        }

        // 开始测试
        async function startTest() {
            try {
                const { testPairs, timeout } = parseConfig();

                // 重置结果
                testResults = [];
                document.getElementById('resultsSection').style.display = 'none';
                document.getElementById('progressContainer').style.display = 'block';

                const totalTests = testPairs.length;
                let completedTests = 0;

                // 更新进度
                function updateProgress() {
                    const progress = (completedTests / totalTests) * 100;
                    document.getElementById('progressFill').style.width = progress + '%';
                    document.getElementById('progressText').textContent =
                        `测试进度: ${completedTests}/${totalTests} (${Math.round(progress)}%)`;
                }

                updateProgress();

                // 并发测试所有组合
                const promises = testPairs.map(pair =>
                    testSingleFile(pair.cdn, pair.file, timeout).then(result => {
                        testResults.push(result);
                        completedTests++;
                        updateProgress();
                    })
                );

                await Promise.all(promises);

                // 显示结果
                displayResults();
                document.getElementById('progressContainer').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'block';

            } catch (error) {
                alert(error.message);
                document.getElementById('progressContainer').style.display = 'none';
            }
        }

        // 测试单个文件
        async function testSingleFile(cdnUrl, fileName, timeout) {
            const fullUrl = `${cdnUrl}/${fileName}`;
            const startTime = performance.now();

            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                const response = await fetch(fullUrl, {
                    signal: controller.signal,
                    cache: 'no-cache'
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                // 确保完全下载
                await response.blob();
                const endTime = performance.now();
                const loadTime = Math.round(endTime - startTime);

                return {
                    cdn: cdnUrl,
                    file: fileName,
                    loadTime: loadTime,
                    status: '成功',
                    url: fullUrl
                };
            } catch (error) {
                const endTime = performance.now();
                const loadTime = Math.round(endTime - startTime);

                let errorMsg = '失败';
                if (error.name === 'AbortError') {
                    errorMsg = '超时';
                } else if (error.message.includes('HTTP')) {
                    errorMsg = error.message;
                } else {
                    errorMsg = '网络错误';
                }

                return {
                    cdn: cdnUrl,
                    file: fileName,
                    loadTime: loadTime,
                    status: errorMsg,
                    url: fullUrl
                };
            }
        }

        // 显示结果
        function displayResults() {
            const tbody = document.getElementById('resultsBody');
            tbody.innerHTML = '';

            // 找出最快的结果
            const successResults = testResults.filter(r => r.status === '成功');
            const fastestTime = successResults.length > 0 ? Math.min(...successResults.map(r => r.loadTime)) : null;

            // 按CDN分组并排序
            const groupedResults = {};
            testResults.forEach(result => {
                if (!groupedResults[result.cdn]) {
                    groupedResults[result.cdn] = [];
                }
                groupedResults[result.cdn].push(result);
            });

            // 对每个CDN组内的结果按加载时间排序
            Object.keys(groupedResults).forEach(cdn => {
                groupedResults[cdn].sort((a, b) => {
                    // 成功的结果优先，然后按加载时间排序
                    if (a.status === '成功' && b.status !== '成功') return -1;
                    if (a.status !== '成功' && b.status === '成功') return 1;
                    if (a.status === '成功' && b.status === '成功') {
                        return a.loadTime - b.loadTime;
                    }
                    return 0;
                });
            });

            // 获取所有唯一的CDN地址并分配颜色
            const uniqueCdns = Object.keys(groupedResults).sort();
            const cdnColorMap = {};
            uniqueCdns.forEach((cdn, index) => {
                cdnColorMap[cdn] = index % 10; // 支持最多10种颜色
            });

            // 使用新的排序显示方法
            displayResultsWithSort();
        }

        // 显示汇总信息
        function displaySummary() {
            const summaryContainer = document.getElementById('summaryCards');
            summaryContainer.innerHTML = '';

            // 获取CDN颜色映射
            const uniqueCdns = [...new Set(testResults.map(r => r.cdn))];
            const cdnColorMap = {};
            uniqueCdns.forEach((cdn, index) => {
                cdnColorMap[cdn] = index % 10;
            });

            // 按CDN分组计算平均时间
            const cdnGroups = {};
            testResults.forEach(result => {
                if (!cdnGroups[result.cdn]) {
                    cdnGroups[result.cdn] = { total: 0, count: 0, success: 0 };
                }
                if (result.status === '成功') {
                    cdnGroups[result.cdn].total += result.loadTime;
                    cdnGroups[result.cdn].success++;
                }
                cdnGroups[result.cdn].count++;
            });

            // 创建汇总卡片
            Object.entries(cdnGroups).forEach(([cdn, data]) => {
                const avgTime = data.success > 0 ? Math.round(data.total / data.success) : 0;
                const successRate = Math.round((data.success / data.count) * 100);
                const colorIndex = cdnColorMap[cdn];

                const card = document.createElement('div');
                card.className = `summary-card cdn-color-${colorIndex}`;
                card.style.borderTop = `4px solid ${getCdnColor(colorIndex)}`;
                card.innerHTML = `
                    <h4 title="${cdn}">${cdn}</h4>
                    <div class="value">${avgTime}ms</div>
                    <div>平均加载时间</div>
                    <div style="margin-top: 10px; color: #666;">
                        成功率: ${successRate}% (${data.success}/${data.count})
                    </div>
                `;
                summaryContainer.appendChild(card);
            });
        }

        // 获取CDN对应的颜色
        function getCdnColor(index) {
            const colors = [
                '#4facfe', '#ffc107', '#28a745', '#dc3545', '#667eea',
                '#ff6384', '#36a2eb', '#ff9f40', '#9966ff', '#4bc0c0'
            ];
            return colors[index % colors.length];
        }

        // 表格排序
        function sortTable(columnIndex) {
            // 重新显示结果，应用新的排序
            displayResultsWithSort(columnIndex);
        }

        // 带排序的结果显示
        function displayResultsWithSort(sortColumn = null) {
            const tbody = document.getElementById('resultsBody');
            tbody.innerHTML = '';

            // 找出最快的结果
            const successResults = testResults.filter(r => r.status === '成功');
            const fastestTime = successResults.length > 0 ? Math.min(...successResults.map(r => r.loadTime)) : null;

            // 复制结果数组进行排序
            let sortedResults = [...testResults];

            if (sortColumn !== null) {
                // 确定排序方向
                const currentDirection = sortDirection[sortColumn] || 'asc';
                const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
                sortDirection = { [sortColumn]: newDirection };

                // 根据列进行排序
                sortedResults.sort((a, b) => {
                    let aVal, bVal;

                    switch(sortColumn) {
                        case 0: // CDN地址
                            aVal = a.cdn;
                            bVal = b.cdn;
                            break;
                        case 1: // 文件名
                            aVal = a.file;
                            bVal = b.file;
                            break;
                        case 2: // 加载时间
                            aVal = a.status === '成功' ? a.loadTime : Infinity;
                            bVal = b.status === '成功' ? b.loadTime : Infinity;
                            break;
                        case 3: // 状态
                            aVal = a.status;
                            bVal = b.status;
                            break;
                        default:
                            return 0;
                    }

                    if (aVal < bVal) return newDirection === 'asc' ? -1 : 1;
                    if (aVal > bVal) return newDirection === 'asc' ? 1 : -1;
                    return 0;
                });
            } else {
                // 默认按CDN分组排序
                const groupedResults = {};
                sortedResults.forEach(result => {
                    if (!groupedResults[result.cdn]) {
                        groupedResults[result.cdn] = [];
                    }
                    groupedResults[result.cdn].push(result);
                });

                // 对每个CDN组内的结果按加载时间排序
                Object.keys(groupedResults).forEach(cdn => {
                    groupedResults[cdn].sort((a, b) => {
                        if (a.status === '成功' && b.status !== '成功') return -1;
                        if (a.status !== '成功' && b.status === '成功') return 1;
                        if (a.status === '成功' && b.status === '成功') {
                            return a.loadTime - b.loadTime;
                        }
                        return 0;
                    });
                });

                // 重新组织为平铺数组
                sortedResults = [];
                Object.keys(groupedResults).sort().forEach(cdn => {
                    sortedResults.push(...groupedResults[cdn]);
                });
            }

            // 获取所有唯一的CDN地址并分配颜色
            const uniqueCdns = [...new Set(sortedResults.map(r => r.cdn))];
            const cdnColorMap = {};
            uniqueCdns.forEach((cdn, index) => {
                cdnColorMap[cdn] = index % 10;
            });

            // 填充表格
            sortedResults.forEach(result => {
                const row = tbody.insertRow();
                const isFastest = result.status === '成功' && result.loadTime === fastestTime;
                const cdnColorIndex = cdnColorMap[result.cdn];

                // 设置行的CSS类
                let rowClasses = [`cdn-color-${cdnColorIndex}`];
                if (isFastest) {
                    rowClasses.push('fastest');
                }
                row.className = rowClasses.join(' ');

                // CDN地址列
                const cdnCell = row.insertCell(0);
                cdnCell.textContent = result.cdn;
                cdnCell.title = result.cdn;

                // 文件名列
                const fileCell = row.insertCell(1);
                fileCell.textContent = result.file;
                fileCell.title = result.file;

                // 加载时间列
                const timeCell = row.insertCell(2);
                timeCell.textContent = result.status === '成功' ? result.loadTime : '-';

                // 状态列
                const statusCell = row.insertCell(3);
                statusCell.textContent = result.status;
                statusCell.className = result.status === '成功' ? 'status-success' : 'status-error';
                statusCell.title = result.status === '成功' ? '加载成功' : result.status;
            });
        }

        // 保存配置
        function saveConfig() {
            const mode = document.querySelector('input[name="configMode"]:checked').value;
            const config = {
                mode: mode,
                timeout: document.getElementById('timeout').value
            };

            if (mode === 'unified') {
                config.cdnUrls = document.getElementById('cdnUrls').value;
                config.testFiles = document.getElementById('testFiles').value;
            } else {
                config.cdnConfigs = document.getElementById('cdnConfigs').value;
            }

            const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'cdn-test-config.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 加载配置
        function loadConfig() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const config = JSON.parse(e.target.result);

                            // 设置配置模式
                            const modeRadio = document.querySelector(`input[name="configMode"][value="${config.mode || 'unified'}"]`);
                            if (modeRadio) {
                                modeRadio.checked = true;
                                toggleConfigMode();
                            }

                            // 加载配置数据
                            if (config.mode === 'individual' && config.cdnConfigs) {
                                document.getElementById('cdnConfigs').value = config.cdnConfigs;
                            } else {
                                document.getElementById('cdnUrls').value = config.cdnUrls || '';
                                document.getElementById('testFiles').value = config.testFiles || '';
                            }

                            document.getElementById('timeout').value = config.timeout || 10;
                            alert('配置加载成功！');
                        } catch (error) {
                            alert('配置文件格式错误！');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // 导出结果
        function exportResults() {
            if (testResults.length === 0) {
                alert('没有测试结果可导出！');
                return;
            }

            const csv = [
                ['CDN地址', '文件名', '加载时间(ms)', '状态', '完整URL'],
                ...testResults.map(r => [r.cdn, r.file, r.loadTime, r.status, r.url])
            ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `cdn-test-results-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 清空结果
        function clearResults() {
            if (confirm('确定要清空所有测试结果吗？')) {
                testResults = [];
                document.getElementById('resultsSection').style.display = 'none';
            }
        }
    </script>
</body>
</html>
